import React, {useState, useEffect, useMemo, useCallback} from "react";
import {Modal, Spin, Alert, Tabs, Button, Space} from "antd";
import {FileWordOutlined, ReloadOutlined, DownloadOutlined} from "@ant-design/icons";
import {isEqual} from "lodash";
import {memo} from "react";

export interface IWordViewerProps {
  /** URL của file Word cần hiển thị */
  fileUrl: string;
  /** Tên file để hiển thị trong title */
  fileName?: string;
  /** Trạng thái mở/đóng modal */
  open: boolean;
  /** Callback khi đóng modal */
  onClose: () => void;
  /** Width của modal (default: "95vw") */
  width?: string | number;
  /** Height của modal body (default: "85vh") */
  height?: string | number;
  /** Có destroy modal khi close không (default: true) */
  destroyOnClose?: boolean;
  /** Custom style cho container */
  containerStyle?: React.CSSProperties;
  /** Viewer mode: 'office' | 'mammoth' | 'both' (default: 'both') */
  viewerMode?: "office" | "mammoth" | "both";
}

const WordViewerComponent: React.FC<IWordViewerProps> = ({
  fileUrl,
  fileName = "Word Document",
  open,
  onClose,
  width = "95vw",
  height = "85vh",
  destroyOnClose = true,
  containerStyle = {},
  viewerMode = "both",
}) => {
  const [loading, setLoading] = useState(false);
  const [htmlContent, setHtmlContent] = useState<string>("");
  const [error, setError] = useState<string>("");
  const [activeTab, setActiveTab] = useState<string>("office");

  // Microsoft Office Online Viewer URL
  const officeViewerUrl = useMemo(() => {
    if (!fileUrl) return "";
    return `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(fileUrl)}`;
  }, [fileUrl]);

  // Load Word document using Mammoth.js
  const loadWordDocument = useCallback(async () => {
    if (!fileUrl || viewerMode === "office") return;

    setLoading(true);
    setError("");

    try {
      // Dynamic import mammoth.js
      const mammoth = await import("mammoth");

      // Fetch the Word document
      const response = await fetch(fileUrl);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const arrayBuffer = await response.arrayBuffer();

      // Convert to HTML
      const result = await mammoth.convertToHtml({arrayBuffer});
      setHtmlContent(result.value);

      if (result.messages.length > 0) {
        console.warn("Mammoth conversion warnings:", result.messages);
      }
    } catch (err) {
      console.error("Error loading Word document:", err);
      setError(err instanceof Error ? err.message : "Không thể tải file Word");
    } finally {
      setLoading(false);
    }
  }, [fileUrl, viewerMode]);

  // Load document when modal opens
  useEffect(() => {
    if (open && fileUrl) {
      loadWordDocument();
    }
  }, [open, fileUrl, loadWordDocument]);

  // Reset state when modal closes
  useEffect(() => {
    if (!open) {
      setHtmlContent("");
      setError("");
      setLoading(false);
    }
  }, [open]);

  // Default container style
  const defaultContainerStyle: React.CSSProperties = {
    height: "100%",
    width: "100%",
    border: "1px solid #d9d9d9",
    borderRadius: "6px",
    overflow: "hidden",
    ...containerStyle,
  };

  // Office Viewer Component
  const OfficeViewer = () => (
    <div style={defaultContainerStyle}>
      <iframe
        src={officeViewerUrl}
        style={{
          width: "100%",
          height: "100%",
          border: "none",
        }}
        title={`Office Viewer - ${fileName}`}
      />
    </div>
  );

  // Mammoth HTML Viewer Component
  const MammothViewer = () => {
    if (loading) {
      return (
        <div style={{...defaultContainerStyle, display: "flex", alignItems: "center", justifyContent: "center"}}>
          <Spin size="large" tip="Đang tải file Word..." />
        </div>
      );
    }

    if (error) {
      return (
        <div style={defaultContainerStyle}>
          <Alert
            message="Lỗi tải file"
            description={error}
            type="error"
            showIcon
            action={
              <Button size="small" icon={<ReloadOutlined />} onClick={loadWordDocument}>
                Thử lại
              </Button>
            }
          />
        </div>
      );
    }

    return (
      <div style={defaultContainerStyle}>
        <div
          style={{
            padding: "20px",
            height: "100%",
            overflow: "auto",
            backgroundColor: "#fff",
          }}
          dangerouslySetInnerHTML={{__html: htmlContent}}
        />
      </div>
    );
  };

  // Tab items
  const tabItems = useMemo(() => {
    const items = [];

    if (viewerMode === "office" || viewerMode === "both") {
      items.push({
        key: "office",
        label: (
          <span>
            <FileWordOutlined />
            Office Online
          </span>
        ),
        children: <OfficeViewer />,
      });
    }

    if (viewerMode === "mammoth" || viewerMode === "both") {
      items.push({
        key: "mammoth",
        label: (
          <span>
            <FileWordOutlined />
            HTML Preview
          </span>
        ),
        children: <MammothViewer />,
      });
    }

    return items;
  }, [viewerMode, officeViewerUrl, htmlContent, loading, error]);

  // Single viewer mode
  if (viewerMode === "office") {
    return (
      <Modal
        title={`Xem Word: ${fileName}`}
        open={open}
        onCancel={onClose}
        width={width}
        style={{top: 10}}
        styles={{
          body: {
            height: height,
            padding: 0,
          },
        }}
        footer={null}
        destroyOnClose={destroyOnClose}
        maskClosable={true}
        keyboard={true}>
        <OfficeViewer />
      </Modal>
    );
  }

  if (viewerMode === "mammoth") {
    return (
      <Modal
        title={`Xem Word: ${fileName}`}
        open={open}
        onCancel={onClose}
        width={width}
        style={{top: 10}}
        styles={{
          body: {
            height: height,
            padding: 0,
          },
        }}
        footer={null}
        destroyOnClose={destroyOnClose}
        maskClosable={true}
        keyboard={true}>
        <MammothViewer />
      </Modal>
    );
  }

  // Both modes with tabs
  return (
    <Modal
      title={
        <Space>
          <span>Xem Word: {fileName}</span>
          <Button type="link" size="small" icon={<DownloadOutlined />} href={fileUrl} target="_blank">
            Tải về
          </Button>
        </Space>
      }
      open={open}
      onCancel={onClose}
      width={width}
      style={{top: 10}}
      styles={{
        body: {
          height: height,
          padding: 0,
        },
      }}
      footer={null}
      destroyOnClose={destroyOnClose}
      maskClosable={true}
      keyboard={true}>
      <Tabs activeKey={activeTab} onChange={setActiveTab} items={tabItems} style={{height: "100%"}} tabBarStyle={{margin: 0, paddingLeft: 16, paddingRight: 16}} />
    </Modal>
  );
};

WordViewerComponent.displayName = "WordViewer";
export const WordViewer = memo(WordViewerComponent, isEqual);
export default WordViewer;
