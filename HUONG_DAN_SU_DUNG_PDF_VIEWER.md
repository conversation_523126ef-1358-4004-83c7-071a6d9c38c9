# Hướng dẫn sử dụng chức năng xem PDF

## 🎯 Tổng quan
Chức năng xem PDF đã được tích hợp vào component `ModalQuanLyFileCaNhan` cho phép người dùng xem trực tiếp file PDF mà không cần tải về.

## 📋 Các bước sử dụng

### 1. Mở Modal Quản lý File
```typescript
// Sử dụng ref để mở modal
const modalRef = useRef<IModalQuanLyFileCaNhanRef>(null);

// Mở modal
modalRef.current?.open();
```

### 2. Tìm file PDF
- Duyệt qua các folder để tìm file PDF
- File PDF sẽ có icon 📄 màu đỏ
- Sử dụng tính năng tìm kiếm nếu cần

### 3. Xem PDF
- Click vào nút **"..."** (More) bên cạnh file PDF
- Chọn **"Xem PDF"** từ menu popup
- Modal PDF viewer sẽ mở ra

## 🔧 Tính năng của PDF Viewer

### ✅ Đã có sẵn:
- **Zoom In/Out**: Sử dụng toolbar hoặc Ctrl + Mouse Wheel
- **Navigation**: Chuyển trang bằng scroll hoặc toolbar
- **Full Screen**: Modal chiếm 95% viewport
- **Responsive**: Tự động điều chỉnh theo màn hình
- **Close**: Click nút X hoặc click outside để đóng

### 🎨 Giao diện:
- Modal size: 95vw x 85vh
- Border: 1px solid #d9d9d9
- Border radius: 6px
- Top position: 10px từ đầu màn hình

## 🛠️ Cấu hình kỹ thuật

### Dependencies đã sử dụng:
```json
{
  "@react-pdf-viewer/core": "^3.12.0",
  "@react-pdf-viewer/zoom": "^3.12.0", 
  "pdfjs-dist": "^3.0.279"
}
```

### Import cần thiết:
```typescript
import {Viewer, Worker} from "@react-pdf-viewer/core";
import "@react-pdf-viewer/core/lib/styles/index.css";
import {zoomPlugin} from "@react-pdf-viewer/zoom";
import "@react-pdf-viewer/zoom/lib/styles/index.css";
import workerUrl from "pdfjs-dist/build/pdf.worker.js?url";
```

## 🔍 Cách hoạt động

### 1. Kiểm tra file extension:
```typescript
{item.loai === "FILE" && arrExtensionPdf.includes(item.extension || "") && (
  <Button icon={<EyeOutlined />} onClick={() => openPdfViewer(item)}>
    Xem PDF
  </Button>
)}
```

### 2. Render PDF:
```typescript
<Worker workerUrl={workerUrl}>
  <Viewer 
    fileUrl={env.VITE_BASE_URL + selectedPdfFile.url_file}
    plugins={[zoomPlugin()]}
  />
</Worker>
```

## 🚀 Tính năng nâng cao có thể thêm

### 1. Toolbar tùy chỉnh:
```typescript
import {toolbarPlugin} from "@react-pdf-viewer/toolbar";
```

### 2. Thumbnail navigation:
```typescript
import {thumbnailPlugin} from "@react-pdf-viewer/thumbnail";
```

### 3. Search trong PDF:
```typescript
import {searchPlugin} from "@react-pdf-viewer/search";
```

### 4. Print PDF:
```typescript
import {printPlugin} from "@react-pdf-viewer/print";
```

## 🐛 Troubleshooting

### Lỗi thường gặp:
1. **PDF không load**: Kiểm tra URL file và CORS policy
2. **Worker error**: Đảm bảo workerUrl được import đúng
3. **Style bị lỗi**: Import đầy đủ CSS files
4. **Zoom không hoạt động**: Kiểm tra zoomPlugin import

### Debug:
```typescript
// Log URL để kiểm tra
console.log('PDF URL:', env.VITE_BASE_URL + selectedPdfFile.url_file);

// Kiểm tra file extension
console.log('File extension:', selectedPdfFile.extension);
```

## 📝 Notes
- Chỉ file có extension trong `arrExtensionPdf` mới hiển thị nút "Xem PDF"
- Modal sử dụng `destroyOnClose` để tối ưu performance
- PDF viewer tự động responsive với màn hình
- Hỗ trợ keyboard shortcuts (Ctrl+Zoom)
