# PdfViewer Component

Component tái sử dụng để hiển thị file PDF trong modal với đầy đủ tính năng zoom, navigation.

## 🚀 C<PERSON>ch sử dụng cơ bản

### 1. Import component
```typescript
import { PdfViewer } from "@src/components/PdfViewer";
```

### 2. Sử dụng với state management thủ công
```typescript
const [isPdfOpen, setIsPdfOpen] = useState(false);
const [pdfUrl, setPdfUrl] = useState("");

<PdfViewer
  fileUrl={pdfUrl}
  fileName="Document.pdf"
  open={isPdfOpen}
  onClose={() => setIsPdfOpen(false)}
/>
```

### 3. Sử dụng với custom hook (khuyến nghị)
```typescript
import { PdfViewer, usePdfViewer } from "@src/components/PdfViewer";

const MyComponent = () => {
  const { isOpen, openPdfViewer, closePdfViewer, currentFileUrl, currentFileName } = usePdfViewer();

  const handleViewPdf = () => {
    openPdfViewer("https://example.com/document.pdf", "My Document");
  };

  return (
    <>
      <Button onClick={handleViewPdf}>Xem PDF</Button>
      
      <PdfViewer
        fileUrl={currentFileUrl || ""}
        fileName={currentFileName || ""}
        open={isOpen}
        onClose={closePdfViewer}
      />
    </>
  );
};
```

## 🎨 Preset Components

### PdfViewerSmall - Modal nhỏ (70vw x 60vh)
```typescript
import { PdfViewerSmall } from "@src/components/PdfViewer/PdfViewerPresets";

<PdfViewerSmall
  fileUrl={pdfUrl}
  fileName="Document.pdf"
  open={isOpen}
  onClose={onClose}
/>
```

### PdfViewerMedium - Modal trung bình (85vw x 75vh)
```typescript
import { PdfViewerMedium } from "@src/components/PdfViewer/PdfViewerPresets";
```

### PdfViewerFullscreen - Full screen (98vw x 90vh)
```typescript
import { PdfViewerFullscreen } from "@src/components/PdfViewer/PdfViewerPresets";
```

### PdfViewerReadOnly - Chỉ xem, không zoom
```typescript
import { PdfViewerReadOnly } from "@src/components/PdfViewer/PdfViewerPresets";
```

### PdfViewerMobile - Tối ưu cho mobile
```typescript
import { PdfViewerMobile } from "@src/components/PdfViewer/PdfViewerPresets";
```

## 📋 Props API

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `fileUrl` | `string` | - | **Required.** URL của file PDF |
| `fileName` | `string` | `"PDF Document"` | Tên file hiển thị trong title |
| `open` | `boolean` | - | **Required.** Trạng thái mở/đóng modal |
| `onClose` | `() => void` | - | **Required.** Callback khi đóng modal |
| `width` | `string \| number` | `"95vw"` | Width của modal |
| `height` | `string \| number` | `"85vh"` | Height của modal body |
| `enableZoom` | `boolean` | `true` | Có hiển thị zoom plugin không |
| `destroyOnClose` | `boolean` | `true` | Có destroy modal khi close không |
| `containerStyle` | `React.CSSProperties` | `{}` | Custom style cho container PDF |

## 🔧 Custom Hook API

### usePdfViewer()

Returns:
```typescript
{
  isOpen: boolean;
  openPdfViewer: (fileUrl: string, fileName?: string) => void;
  closePdfViewer: () => void;
  currentFileUrl: string | null;
  currentFileName: string | null;
}
```

## 💡 Ví dụ nâng cao

### Custom styling
```typescript
<PdfViewer
  fileUrl={pdfUrl}
  fileName="Custom Document"
  open={isOpen}
  onClose={onClose}
  width="80vw"
  height="70vh"
  containerStyle={{
    border: "2px solid #1890ff",
    borderRadius: "12px",
    boxShadow: "0 4px 12px rgba(0,0,0,0.15)"
  }}
/>
```

### Disable zoom
```typescript
<PdfViewer
  fileUrl={pdfUrl}
  fileName="Read Only Document"
  open={isOpen}
  onClose={onClose}
  enableZoom={false}
/>
```

## 🔄 Migration từ code cũ

### Trước đây:
```typescript
// Code cũ - inline PDF viewer
const [isPdfViewerOpen, setIsPdfViewerOpen] = useState(false);
const [selectedPdfFile, setSelectedPdfFile] = useState(null);

<Modal title="Xem PDF" open={isPdfViewerOpen} onCancel={() => setIsPdfViewerOpen(false)}>
  <Worker workerUrl={workerUrl}>
    <Viewer fileUrl={selectedPdfFile?.url} plugins={[zoomPlugin()]} />
  </Worker>
</Modal>
```

### Bây giờ:
```typescript
// Code mới - sử dụng PdfViewer component
import { PdfViewer, usePdfViewer } from "@src/components/PdfViewer";

const { isOpen, openPdfViewer, closePdfViewer, currentFileUrl, currentFileName } = usePdfViewer();

<PdfViewer
  fileUrl={currentFileUrl || ""}
  fileName={currentFileName || ""}
  open={isOpen}
  onClose={closePdfViewer}
/>
```

## 🎯 Lợi ích

✅ **Tái sử dụng**: Dùng ở bất kỳ đâu trong dự án
✅ **Consistent UI**: Giao diện thống nhất
✅ **Performance**: Tối ưu với useMemo, memo
✅ **Flexible**: Nhiều preset và custom options
✅ **Type Safe**: Full TypeScript support
✅ **Easy to use**: Custom hook đơn giản
